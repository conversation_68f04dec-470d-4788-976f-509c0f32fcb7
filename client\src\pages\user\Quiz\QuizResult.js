import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { message } from 'antd';
import Confetti from 'react-confetti';
import useWindowSize from 'react-use/lib/useWindowSize';
import { TbArrowLeft } from 'react-icons/tb';
import { getExamById } from '../../../apicalls/exams';
import { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import ContentRenderer from '../../../components/ContentRenderer';
import Pass from '../../../assets/pass.gif';
import Fail from '../../../assets/fail.gif';
import PassSound from '../../../assets/pass.mp3';
import FailSound from '../../../assets/fail.mp3';
import './responsive.css';

const QuizResult = () => {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [explanations, setExplanations] = useState({});
  const [showReview, setShowReview] = useState(false);
  
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { width, height } = useWindowSize();
  
  const result = location.state?.result;

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getExamById({ examId: id });
        dispatch(HideLoading());
        
        if (response.success) {
          setExamData(response.data);
          setQuestions(response.data?.questions || []);
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
        navigate('/user/quiz');
      }
    };

    if (id) {
      fetchExamData();
    }
  }, [id, dispatch, navigate]);

  useEffect(() => {
    if (result) {
      // Play sound based on result
      new Audio(result.verdict === "Pass" ? PassSound : FailSound).play();
    }
  }, [result]);

  // Add quiz-fullscreen class for fullscreen experience
  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');

    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {
    try {
      dispatch(ShowLoading());
      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });
      dispatch(HideLoading());

      if (response.success) {
        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));
      } else {
        message.error(response.error || "Failed to fetch explanation.");
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  if (!result || !examData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (showReview) {
    return (
      <div className="min-h-screen bg-gray-50 py-4 sm:py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-6 sm:mb-8">
            <div className="bg-white rounded-lg p-4 sm:p-6 shadow-sm border border-gray-200">
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">Review Your Answers</h2>
              <p className="text-gray-600 text-sm sm:text-base">Detailed breakdown of your quiz performance</p>
            </div>
          </div>

          {/* Questions Review */}
          <div className="space-y-4 sm:space-y-6 mb-6 sm:mb-8">
            {questions.map((question, index) => {
              const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||
                                result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || "";
              const isCorrect = result.correctAnswers.some(q => q._id === question._id);

              return (
                <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                  {/* Question Header */}
                  <div className={`px-4 sm:px-6 py-3 sm:py-4 border-b ${
                    isCorrect
                      ? 'bg-green-50 border-green-200'
                      : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center justify-between">
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                        Question {index + 1}
                      </h3>
                      <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${
                        isCorrect
                          ? 'bg-green-100 text-green-700 border border-green-200'
                          : 'bg-red-100 text-red-700 border border-red-200'
                      }`}>
                        {isCorrect ? '✓ Correct' : '✗ Incorrect'}
                      </span>
                    </div>
                  </div>

                  {/* Question Content */}
                  <div className="p-4 sm:p-6">
                    <h4 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">{question.name}</h4>

                    {(question.image || question.imageUrl) && (
                      <div className="mb-3 sm:mb-4">
                        <img
                          src={question.image || question.imageUrl}
                          alt="Question"
                          className="max-w-full h-auto rounded-lg border border-gray-200 mx-auto"
                          style={{ maxHeight: '300px' }}
                        />
                      </div>
                    )}

                    {/* Answer Comparison */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 mb-3 sm:mb-4">
                      <div className={`p-3 sm:p-4 rounded-lg border-2 ${
                        isCorrect
                          ? 'border-green-300 bg-green-50'
                          : 'border-red-300 bg-red-50'
                      }`}>
                        <h5 className="font-semibold text-gray-700 mb-2 text-sm sm:text-base">Your Answer</h5>
                        <p className={`text-sm sm:text-base font-medium ${
                          isCorrect ? 'text-green-800' : 'text-red-800'
                        }`}>
                          {question.type === "mcq" || question.answerType === "Options"
                            ? question.options?.[userAnswer] || "Not answered"
                            : userAnswer || "Not answered"}
                        </p>
                      </div>

                      <div className="p-3 sm:p-4 rounded-lg border-2 border-green-300 bg-green-50">
                        <h5 className="font-semibold text-gray-700 mb-2 text-sm sm:text-base">Correct Answer</h5>
                        <p className="text-green-800 text-sm sm:text-base font-medium">
                          {question.type === "mcq" || question.answerType === "Options"
                            ? question.options?.[question.correctOption || question.correctAnswer]
                            : (question.correctAnswer || question.correctOption)}
                        </p>
                      </div>
                    </div>

                    {/* Explanation Section */}
                    {!isCorrect && (
                      <div className="mt-3 sm:mt-4 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <button
                          className="px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base"
                          onClick={() => fetchExplanation(
                            question.name,
                            question.type === "mcq" || question.answerType === "Options"
                              ? question.options?.[question.correctOption || question.correctAnswer]
                              : (question.correctAnswer || question.correctOption),
                            question.type === "mcq" || question.answerType === "Options"
                              ? question.options?.[userAnswer] || "Not answered"
                              : userAnswer || "Not answered",
                            question.image || question.imageUrl
                          )}
                        >
                          Get AI Explanation
                        </button>

                        {explanations[question.name] && (
                          <div className="mt-3 sm:mt-4 p-3 sm:p-4 bg-white rounded-lg border border-blue-200">
                            <h6 className="font-semibold text-blue-800 mb-2 text-sm sm:text-base">AI Explanation</h6>
                            <div className="text-sm sm:text-base">
                              <ContentRenderer text={explanations[question.name]} />
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Back Buttons */}
          <div className="text-center space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <button
              className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors duration-200 text-sm sm:text-base"
              onClick={() => setShowReview(false)}
            >
              Back to Results
            </button>
            <button
              className="w-full sm:w-auto px-4 sm:px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base"
              onClick={() => navigate('/user/quiz')}
            >
              Hub
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 sm:py-8">
      {result.verdict === "Pass" && <Confetti width={width} height={height} />}

      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {/* Header */}
          <div className={`px-4 sm:px-6 py-8 sm:py-12 text-center ${
            result.verdict === "Pass" ? "bg-green-50" : "bg-red-50"
          }`}>
            <img
              src={result.verdict === "Pass" ? Pass : Fail}
              alt={result.verdict}
              className="mx-auto mb-4 sm:mb-6 w-20 h-20 sm:w-28 sm:h-28 md:w-32 md:h-32"
            />
            <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4 ${
              result.verdict === "Pass" ? "text-green-700" : "text-red-700"
            }`}>
              {result.verdict === "Pass" ? "Congratulations!" : "Better Luck Next Time!"}
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 px-2">
              {result.verdict === "Pass"
                ? "You've successfully passed the quiz!"
                : "Keep practicing and try again!"}
            </p>
          </div>

          {/* Statistics */}
          <div className="p-4 sm:p-6 md:p-8">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8">
              <div className="text-center p-3 sm:p-4 md:p-6 bg-green-50 rounded-xl border border-green-100">
                <div className="text-2xl sm:text-3xl font-bold text-green-600">
                  {result.correctAnswers?.length || 0}
                </div>
                <div className="text-xs sm:text-sm text-green-700 font-medium mt-1">Correct</div>
              </div>

              <div className="text-center p-3 sm:p-4 md:p-6 bg-red-50 rounded-xl border border-red-100">
                <div className="text-2xl sm:text-3xl font-bold text-red-600">
                  {result.wrongAnswers?.length || 0}
                </div>
                <div className="text-xs sm:text-sm text-red-700 font-medium mt-1">Wrong</div>
              </div>

              <div className="text-center p-3 sm:p-4 md:p-6 bg-blue-50 rounded-xl border border-blue-100">
                <div className="text-2xl sm:text-3xl font-bold text-blue-600">
                  {questions.length}
                </div>
                <div className="text-xs sm:text-sm text-blue-700 font-medium mt-1">Total</div>
              </div>

              <div className="text-center p-3 sm:p-4 md:p-6 bg-purple-50 rounded-xl border border-purple-100">
                <div className="text-2xl sm:text-3xl font-bold text-purple-600">
                  {examData.passingMarks}
                </div>
                <div className="text-xs sm:text-sm text-purple-700 font-medium mt-1">Required</div>
              </div>
            </div>

            {/* Score Percentage */}
            <div className="mb-6 sm:mb-8">
              <div className="text-center mb-3 sm:mb-4">
                <span className="text-xl sm:text-2xl font-bold text-gray-700">Your Score</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-4 sm:h-6 overflow-hidden">
                <div
                  className={`h-full rounded-full transition-all duration-1000 ${
                    result.verdict === "Pass" ? "bg-green-600" : "bg-red-600"
                  }`}
                  style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}
                ></div>
              </div>
              <div className="text-center mt-2 sm:mt-3">
                <span className="text-2xl sm:text-3xl font-bold text-gray-700">
                  {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%
                </span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
              <button
                className="px-6 sm:px-8 py-3 sm:py-4 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base"
                onClick={() => setShowReview(true)}
              >
                Review Answers
              </button>

              <button
                className="px-6 sm:px-8 py-3 sm:py-4 bg-green-600 text-white rounded-xl font-semibold hover:bg-green-700 transition-colors duration-200 text-sm sm:text-base"
                onClick={() => navigate(`/quiz/${id}/start`)}
              >
                Retake Quiz
              </button>

              <button
                className="px-6 sm:px-8 py-3 sm:py-4 bg-gray-600 text-white rounded-xl font-semibold hover:bg-gray-700 transition-colors duration-200 text-sm sm:text-base"
                onClick={() => navigate('/user/quiz')}
              >
                Hub
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Back Navigation Arrow */}
      <div className="fixed bottom-4 left-4 z-50">
        <button
          onClick={() => navigate('/user/quiz')}
          className="flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105"
          title="Back to Quizzes"
        >
          <TbArrowLeft className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default QuizResult;
