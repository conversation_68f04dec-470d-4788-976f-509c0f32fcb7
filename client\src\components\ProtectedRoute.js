import { message } from "antd";
import React, { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { getUserInfo } from "../apicalls/users";
import { useDispatch, useSelector } from "react-redux";
import { SetUser } from "../redux/usersSlice.js";
import { useNavigate, useLocation } from "react-router-dom";
import { HideLoading, ShowLoading } from "../redux/loaderSlice";
import { checkPaymentStatus } from "../apicalls/payment.js";
import "./ProtectedRoute.css";
import { SetSubscription } from "../redux/subscriptionSlice.js";
import { setPaymentVerificationNeeded } from "../redux/paymentSlice.js";


function ProtectedRoute({ children }) {
  const { user } = useSelector((state) => state.user);
  const [isPaymentPending, setIsPaymentPending] = useState(false);
  const intervalRef = useRef(null);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const { paymentVerificationNeeded } = useSelector((state) => state.payment);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const activeRoute = location.pathname;





  const getUserData = async () => {
    try {
      const response = await getUserInfo();
      if (response.success) {
        dispatch(SetUser(response.data));
      } else {
        message.error(response.message);
        navigate("/login");
      }
    } catch (error) {
      navigate("/login");
      message.error(error.message);
    }
  };

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      getUserData();
    } else {
      navigate("/login");
    }
  }, []);



  useEffect(() => {
    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {
      navigate('/user/plans');
    }
  }, [isPaymentPending, activeRoute, navigate]);

  const verifyPaymentStatus = async () => {
    try {
      const data = await checkPaymentStatus();
      console.log("Payment Status:", data);
      if (data?.error || data?.paymentStatus !== 'paid') {
        if (subscriptionData !== null) {
          dispatch(SetSubscription(null));
        }
        setIsPaymentPending(true);
      }
      else {
        setIsPaymentPending(false);
        dispatch(SetSubscription(data));
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      }
    } catch (error) {
      console.log("Error checking payment status:", error);
      dispatch(SetSubscription(null));
      setIsPaymentPending(true);
    }
  };

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing 2222222...");

      if (paymentVerificationNeeded) {
        console.log('Inside timer in effect 2....');
        intervalRef.current = setInterval(() => {
          console.log('Timer in action...');
          verifyPaymentStatus();
        }, 15000);
        dispatch(setPaymentVerificationNeeded(false));
      }
    }
  }, [paymentVerificationNeeded]);

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing...");
      verifyPaymentStatus();
    }
  }, [user, activeRoute]);


  const getButtonClass = (title) => {
    // Exclude "Plans" and "Profile" buttons from the "button-disabled" class
    if (!user.paymentRequired || title === "Plans" || title === "Profile" || title === "Logout") {
      return ""; // No class applied
    }

    return subscriptionData?.paymentStatus !== "paid" && user?.paymentRequired
      ? "button-disabled"
      : "";
  };




  return (
    <div className="layout-modern min-h-screen flex flex-col">
      {/* No sidebar - users will use hub for navigation */}


      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Modern Responsive Header */}
        <motion.header
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20"
        >
          <div className="px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10">
            <div className="flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20">
              {/* Left Section - Hub Button */}
              <div className="flex items-center flex-shrink-0">
                <button
                  onClick={() => navigate("/user/hub")}
                  className="hub-button group flex items-center space-x-1 xs:space-x-2 px-3 xs:px-4 sm:px-5 py-2 xs:py-2.5 bg-gradient-to-r from-blue-600 via-blue-500 to-indigo-600 hover:from-blue-700 hover:via-blue-600 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-blue-500/25 text-xs xs:text-sm sm:text-base border border-blue-400/20"
                >
                  <i className="ri-apps-2-line text-sm xs:text-base sm:text-lg group-hover:rotate-12 transition-transform duration-300"></i>
                  <span className="hidden xs:inline tracking-wide">Hub</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                </button>
              </div>

              {/* Center Section - Brainwave Heading */}
              <div className="flex-1 flex justify-center px-2 xs:px-4">
                <div className="relative group">
                  <h1 className="brainwave-heading text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-4xl font-black bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent truncate max-w-full tracking-tight relative z-10">
                    BrainWave
                  </h1>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-indigo-600/20 to-purple-600/20 blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-600/10 via-indigo-600/10 to-purple-600/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                </div>
              </div>

              {/* Right Section - User Profile */}
              <div className="flex items-center space-x-2 xs:space-x-3 sm:space-x-4 flex-shrink-0">
                {/* Desktop/Tablet: Show user info beside avatar */}
                <div className="user-info-desktop text-right hidden md:block">
                  <div className="text-sm lg:text-base font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent truncate max-w-32 lg:max-w-40">
                    {user?.name}
                  </div>
                  <div className="text-xs lg:text-sm text-blue-600 font-semibold px-2 py-0.5 bg-blue-50 rounded-full border border-blue-200">
                    {user?.isAdmin ? "Administrator" : `Class ${user?.class} Student`}
                  </div>
                </div>

                {/* Tablet: Show abbreviated user info */}
                <div className="user-info-tablet text-right hidden sm:block md:hidden">
                  <div className="text-sm font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent truncate max-w-24">
                    {user?.name?.split(' ')[0]}
                  </div>
                  <div className="text-xs text-blue-600 font-semibold px-2 py-0.5 bg-blue-50 rounded-full border border-blue-200">
                    {user?.isAdmin ? "Admin" : `Class ${user?.class}`}
                  </div>
                </div>

                {/* User Profile Avatar */}
                <div className="user-profile-container flex flex-col items-center group">
                  <div className="user-avatar relative w-10 h-10 xs:w-11 xs:h-11 sm:w-12 sm:h-12 md:w-13 md:h-13 lg:w-14 lg:h-14 rounded-full overflow-hidden flex items-center justify-center border-3 border-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-110 hover:-translate-y-1">
                    {user?.profileImage ? (
                      <img
                        src={user.profileImage}
                        alt="Profile"
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 text-white font-bold text-sm xs:text-base sm:text-lg relative overflow-hidden">
                        <span className="relative z-10">{user?.name?.charAt(0)?.toUpperCase() || 'U'}</span>
                        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                    )}
                    <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 rounded-full opacity-0 group-hover:opacity-20 blur transition-opacity duration-300"></div>
                  </div>

                  {/* Mobile: Show name and class below avatar */}
                  <div className="user-info-mobile text-center sm:hidden mt-2">
                    <div className="text-xs font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent leading-tight truncate max-w-16 xs:max-w-20">
                      {user?.name?.split(' ')[0] || 'User'}
                    </div>
                    <div className="text-xs text-blue-600 font-semibold leading-tight px-1.5 py-0.5 bg-blue-50 rounded-full border border-blue-200 mt-1">
                      {user?.isAdmin ? "Admin" : `C${user?.class}`}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50 pb-20 sm:pb-0">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </main>

        {/* Modern Bottom Navigation - Mobile Only */}
        <motion.nav
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="fixed bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white/98 to-white/95 backdrop-blur-xl border-t border-blue-100/50 shadow-2xl shadow-blue-100/20 sm:hidden z-40"
        >
          <div className="px-4 py-3">
            <div className="flex justify-center">
              <button
                onClick={() => navigate("/user/hub")}
                className="group flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl hover:shadow-blue-500/25 border border-blue-400/20 min-w-32 relative overflow-hidden"
              >
                <i className="ri-home-4-line text-lg group-hover:scale-110 transition-transform duration-300"></i>
                <span className="tracking-wide">Back to Hub</span>
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
              </button>
            </div>
          </div>
        </motion.nav>
      </div>
    </div>
  );
}

export default ProtectedRoute;
