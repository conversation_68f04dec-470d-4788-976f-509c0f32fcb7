.button-disabled {
  opacity: 0.5;
  pointer-events: none; /* This will disable clicking on the buttons */
}

/* ===== MODERN NAVIGATION ===== */
.nav-modern {
  transition: all 0.3s ease;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  min-height: 3.5rem;
}

.nav-modern:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* ===== HUB BUTTON ===== */
.hub-button {
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.hub-button:hover {
  box-shadow: 0 4px 16px rgba(0, 123, 255, 0.4);
}

/* ===== BRAINWAVE HEADING ===== */
.brainwave-heading {
  background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

/* ===== USER PROFILE SECTION ===== */
.user-profile-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.user-avatar {
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.user-info-mobile {
  text-align: center;
  line-height: 1.2;
}

.user-info-desktop {
  text-align: right;
  line-height: 1.3;
}

/* ===== RESPONSIVE HEADER ===== */
@media (max-width: 480px) {
  .nav-modern {
    padding: 0 0.75rem;
  }

  .nav-modern .hub-button {
    padding: 0.5rem;
    min-width: 2.5rem;
  }

  .nav-modern .hub-button span {
    display: none;
  }

  .brainwave-heading {
    font-size: 1.125rem;
    font-weight: 700;
  }

  .user-avatar {
    width: 1.5rem;
    height: 1.5rem;
  }

  .user-info-mobile {
    font-size: 0.625rem;
    max-width: 4rem;
  }

  .user-profile-container {
    gap: 0.125rem;
  }
}

@media (min-width: 481px) and (max-width: 640px) {
  .nav-modern .hub-button span {
    display: none;
  }

  .brainwave-heading {
    font-size: 1.25rem;
  }

  .user-avatar {
    width: 1.75rem;
    height: 1.75rem;
  }

  .user-info-mobile {
    font-size: 0.75rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .brainwave-heading {
    font-size: 1.5rem;
  }
}

@media (min-width: 769px) {
  .brainwave-heading {
    font-size: 1.875rem;
  }
}
