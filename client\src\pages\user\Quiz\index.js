import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { Tb<PERSON><PERSON>, Tb<PERSON>earch, Tb<PERSON><PERSON><PERSON>, TbArrowLeft } from 'react-icons/tb';
import { getAllExams } from '../../../apicalls/exams';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { QuizGrid } from '../../../components/modern';
import './responsive.css';
import './style.css';

const Quiz = () => {
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  // Set default class filter to user's class
  const userClass = user?.class || '';

  useEffect(() => {
    const getExams = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getAllExams();
        dispatch(HideLoading());

        if (response.success) {
          // Sort exams by creation date (newest first)
          const sortedExams = response.data.sort((a, b) => {
            return new Date(b.createdAt || b.date || 0) - new Date(a.createdAt || a.date || 0);
          });

          setExams(sortedExams);

          // Set default filter to user's class if available
          if (userClass) {
            setSelectedClass(userClass);
          }
        } else {
          message.error(response.message);
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
      }
    };

    getExams();
  }, [dispatch, userClass]);

  // Filter exams based on search term and selected class
  useEffect(() => {
    let filtered = [...exams];

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by class
    if (selectedClass) {
      filtered = filtered.filter(exam => exam.class === selectedClass);
    }

    // Sort filtered results by newest first
    filtered.sort((a, b) => {
      return new Date(b.createdAt || b.date || 0) - new Date(a.createdAt || a.date || 0);
    });

    setFilteredExams(filtered);
  }, [exams, searchTerm, selectedClass]);

  // Get unique classes for filter dropdown
  const availableClasses = [...new Set(exams.map(exam => exam.class).filter(Boolean))].sort();

  const handleQuizStart = (quiz) => {
    navigate(`/quiz/${quiz._id}/start`);
  };

  return (
    <div className="quiz-listing-container">
      <div className="quiz-listing-content">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="quiz-listing-header"
        >
          <div className="text-center mb-6">
            <h1 className="heading-2 text-gradient mb-4">
              <TbBrain className="inline w-10 h-10 mr-3" />
              Challenge Your Brain, Beat the Rest
            </h1>
          </div>
        </motion.div>

        {/* Search and Filter Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              {/* Search Box */}
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <TbSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search quizzes by name or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                />
              </div>

              {/* Class Filter */}
              <div className="sm:w-48">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <TbFilter className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    value={selectedClass}
                    onChange={(e) => setSelectedClass(e.target.value)}
                    className="block w-full pl-10 pr-8 py-3 border border-gray-300 rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base appearance-none"
                  >
                    <option value="">All Classes</option>
                    {availableClasses.map((className) => (
                      <option key={className} value={className}>
                        Class {className}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Results Summary */}
            <div className="text-center text-gray-600 text-sm">
              {searchTerm || selectedClass ? (
                <p>
                  Showing {filteredExams.length} of {exams.length} quizzes
                  {searchTerm && ` matching "${searchTerm}"`}
                  {selectedClass && ` for Class ${selectedClass}`}
                </p>
              ) : (
                <p>Showing all {exams.length} available quizzes</p>
              )}
            </div>
          </div>
        </motion.div>

        {/* Quiz Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {filteredExams.length > 0 ? (
            <QuizGrid
              quizzes={filteredExams}
              onQuizStart={handleQuizStart}
              className="quiz-grid-container"
            />
          ) : exams.length > 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg mb-2">
                No quizzes found matching your criteria.
              </div>
              <div className="text-gray-400 text-sm">
                Try adjusting your search or filter settings.
              </div>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedClass('');
                }}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                Clear Filters
              </button>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg">
                No quizzes available at the moment.
              </div>
              <div className="text-gray-400 text-sm mt-2">
                Check back later for new challenges!
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* Back Navigation Arrow */}
      <div className="fixed bottom-4 left-4 z-50">
        <button
          onClick={() => navigate('/user/hub')}
          className="flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105"
          title="Back to Hub"
        >
          <TbArrowLeft className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default Quiz;
